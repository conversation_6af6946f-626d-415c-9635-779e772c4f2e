{"logs": [{"outputFile": "com.anonymous.test.app-mergeDebugResources-56:/values-bn/values-bn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3591,3690,3792,3894,3997,4098,4200,11953", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3685,3787,3889,3992,4093,4195,4315,12049"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,10980", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,11062"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1101,1167,1258,1324,1385,1475,1539,1606,1667,1736,1798,1852,1959,2018,2079,2133,2207,2327,2412,2502,2608,2698,2782,2917,2988,3058,3190,3277,3360,3418,3474,3540,3613,3693,3764,3846,3915,3991,4071,4140,4249,4344,4427,4517,4612,4686,4760,4853,4907,4992,5059,5145,5230,5292,5356,5419,5485,5587,5686,5779,5878,5940,6000,6080,6163,6242", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "266,364,457,540,641,733,837,954,1035,1096,1162,1253,1319,1380,1470,1534,1601,1662,1731,1793,1847,1954,2013,2074,2128,2202,2322,2407,2497,2603,2693,2777,2912,2983,3053,3185,3272,3355,3413,3469,3535,3608,3688,3759,3841,3910,3986,4066,4135,4244,4339,4422,4512,4607,4681,4755,4848,4902,4987,5054,5140,5225,5287,5351,5414,5480,5582,5681,5774,5873,5935,5995,6075,6158,6237,6310"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3124,3222,3315,3398,3499,4320,4424,4541,4805,4866,4932,5412,5626,5755,5845,5909,5976,6037,6106,6168,6222,6329,6388,6449,6503,6577,6915,7000,7090,7196,7286,7370,7505,7576,7646,7778,7865,7948,8006,8062,8128,8201,8281,8352,8434,8503,8579,8659,8728,8837,8932,9015,9105,9200,9274,9348,9441,9495,9580,9647,9733,9818,9880,9944,10007,10073,10175,10274,10367,10466,10528,10588,11067,11150,11229", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "316,3217,3310,3393,3494,3586,4419,4536,4617,4861,4927,5018,5473,5682,5840,5904,5971,6032,6101,6163,6217,6324,6383,6444,6498,6572,6692,6995,7085,7191,7281,7365,7500,7571,7641,7773,7860,7943,8001,8057,8123,8196,8276,8347,8429,8498,8574,8654,8723,8832,8927,9010,9100,9195,9269,9343,9436,9490,9575,9642,9728,9813,9875,9939,10002,10068,10170,10269,10362,10461,10523,10583,10663,11145,11224,11297"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "50,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4699,5023,5125,5234", "endColumns": "105,101,108,105", "endOffsets": "4800,5120,5229,5335"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "33,49,57,59,60,62,76,77,78,125,126,127,128,133,134,135,136,137,138,139,140,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3051,4622,5340,5478,5546,5687,6697,6764,6838,10668,10751,10831,10901,11302,11382,11457,11545,11632,11707,11783,11858,12054,12130,12207,12277", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3119,4694,5407,5541,5621,5750,6759,6833,6910,10746,10826,10896,10975,11377,11452,11540,11627,11702,11778,11853,11948,12125,12202,12272,12345"}}]}]}