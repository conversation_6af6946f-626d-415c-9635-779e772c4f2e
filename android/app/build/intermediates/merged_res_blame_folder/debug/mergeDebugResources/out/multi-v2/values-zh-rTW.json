{"logs": [{"outputFile": "com.anonymous.test.app-mergeDebugResources-56:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "39,40,41,42,43,44,45,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3269,3361,3460,3554,3648,3741,3834,10718", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3356,3455,3549,3643,3736,3829,3925,10814"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,9832", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,9906"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,532,602,674,747,822,889,959,1032,1104,1181,1257,1329,1399,1468,1548,1616,1686,1753", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,527,597,669,742,817,884,954,1027,1099,1176,1252,1324,1394,1463,1543,1611,1681,1748,1817"}, "to": {"startLines": "33,49,57,59,60,62,76,77,78,125,126,127,128,133,134,135,136,137,138,139,140,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,4204,4837,4964,5030,5165,6065,6131,6201,9547,9620,9695,9762,10129,10202,10274,10351,10427,10499,10569,10638,10819,10887,10957,11024", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2924,4271,4899,5025,5100,5225,6126,6196,6268,9615,9690,9757,9827,10197,10269,10346,10422,10494,10564,10633,10713,10882,10952,11019,11088"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "50,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4276,4551,4643,4744", "endColumns": "83,91,100,92", "endOffsets": "4355,4638,4739,4832"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,2993,3055,3122,3192,3930,4024,4131,4360,4411,4473,4904,5105,5230,5308,5369,5427,5483,5543,5601,5655,5740,5796,5854,5908,5973,6273,6347,6419,6501,6575,6652,6772,6835,6898,6997,7074,7148,7198,7249,7315,7378,7446,7517,7588,7649,7720,7787,7849,7936,8015,8080,8163,8248,8322,8386,8462,8510,8583,8647,8723,8801,8863,8927,8990,9056,9136,9214,9290,9369,9423,9478,9911,9986,10059", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,2988,3050,3117,3187,3264,4019,4126,4199,4406,4468,4546,4959,5160,5303,5364,5422,5478,5538,5596,5650,5735,5791,5849,5903,5968,6060,6342,6414,6496,6570,6647,6767,6830,6893,6992,7069,7143,7193,7244,7310,7373,7441,7512,7583,7644,7715,7782,7844,7931,8010,8075,8158,8243,8317,8381,8457,8505,8578,8642,8718,8796,8858,8922,8985,9051,9131,9209,9285,9364,9418,9473,9542,9981,10054,10124"}}]}]}