import React, { useCallback } from 'react';
import { Platform } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolate,
  runOnJS,
  useAnimatedReaction,
  Easing,
} from 'react-native-reanimated';
import { AnimatedSharedImageProps } from './types';

/**
 * AnimatedSharedImage - Handles the animated transition between shared elements
 * 
 * @param transitionData - Data containing from/to positions and dimensions
 * @param isActive - Whether the transition is currently active
 * @param onAnimationComplete - Callback fired when animation completes
 * @param imageUrl - URL of the image to animate
 */
export const AnimatedSharedImage: React.FC<AnimatedSharedImageProps> = React.memo(({
  transitionData,
  isActive,
  onAnimationComplete,
  imageUrl,
}) => {
  const progress = useSharedValue(0);
  const { from, to } = transitionData;

  // Calculate transformation values
  const scaleX = to.width / from.width;
  const scaleY = to.height / from.height;
  const targetTranslateX = to.x - from.x;
  const targetTranslateY = to.y - from.y;

  const handleAnimationComplete = useCallback(() => {
    onAnimationComplete();
  }, [onAnimationComplete]);

  useAnimatedReaction(
    () => isActive,
    (current, previous) => {
      if (current && !previous) {
        // Forward animation
        progress.value = withTiming(1, { 
          duration: 500,
          easing: Easing.out(Easing.exp)
        }, (finished) => {
          if (finished) {
            runOnJS(handleAnimationComplete)();
          }
        });
      } else if (!current && previous) {
        // Reverse animation
        progress.value = withTiming(0, { 
          duration: 400,
          easing: Easing.inOut(Easing.quad)
        }, (finished) => {
          if (finished) {
            runOnJS(handleAnimationComplete)();
          }
        });
      }
    }
  );

  const animatedStyle = useAnimatedStyle(() => {
    // Expanding effect (top to bottom)
    const heightProgress = interpolate(
      progress.value,
      [0, 0.8, 1],
      [0, 0.95, 1],
      Extrapolate.CLAMP
    );
    
    // Zoom out effect
    const zoomEffect = interpolate(
      progress.value,
      [0, 0.5, 1],
      [1, 0.97, 1],
      Extrapolate.CLAMP
    );
    
    // Position interpolation
    const currentTranslateX = interpolate(
      progress.value,
      [0, 1],
      [0, targetTranslateX],
      Extrapolate.CLAMP
    );
    
    const currentTranslateY = interpolate(
      progress.value,
      [0, 1],
      [0, targetTranslateY],
      Extrapolate.CLAMP
    );

    // Scale interpolation
    const currentScaleX = interpolate(
      progress.value,
      [0, 1],
      [1, scaleX],
      Extrapolate.CLAMP
    );
    
    const currentScaleY = interpolate(
      progress.value,
      [0, 1],
      [1, scaleY],
      Extrapolate.CLAMP
    );

    return {
      transform: [
        { translateX: currentTranslateX },
        { translateY: currentTranslateY },
        { scaleX: currentScaleX * zoomEffect },
        { scaleY: currentScaleY * zoomEffect },
      ],
      position: 'absolute',
      left: from.x,
      top: from.y,
      width: from.width,
      height: from.height * heightProgress,
      zIndex: 1000,
      overflow: 'hidden',
      borderRadius: interpolate(progress.value, [0, 1], [8, 0]),
    };
  });

  const imageStyle = useAnimatedStyle(() => {
    const heightProgress = interpolate(
      progress.value,
      [0, 0.8, 1],
      [0, 0.95, 1],
      Extrapolate.CLAMP
    );
    
    return {
      width: '100%',
      height: '100%',
      resizeMode: 'cover' as const,
      transform: [
        { translateY: interpolate(heightProgress, [0, 1], [from.height/2, 0]) }
      ]
    };
  });

  if (!isActive) return null;

  return (
    <Animated.View style={animatedStyle}>
      <Animated.Image 
        source={{ uri: imageUrl }} 
        style={imageStyle}
        resizeMode="cover"
        accessibilityLabel="Transitioning image"
      />
    </Animated.View>
  );
});

AnimatedSharedImage.displayName = 'AnimatedSharedImage';