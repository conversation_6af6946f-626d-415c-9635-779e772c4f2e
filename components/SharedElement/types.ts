export interface SharedElementData {
  x: number;
  y: number;
  width: number;
  height: number;
  id: string;
}

export interface TransitionData {
  from: SharedElementData;
  to: SharedElementData;
  fromId: string;
  toId: string;
}

export interface SharedElementContextType {
  registerElement: (id: string, element: SharedElementData) => void;
  unregisterElement: (id: string) => void;
  startTransition: (fromId: string, toId: string, callback: () => void) => void;
  endTransition: () => void;
  activeTransition: { fromId: string; toId: string } | null;
  transitionData: TransitionData | null;
  sharedElements: Record<string, SharedElementData>;
}

export interface SharedElementProps {
  id: string;
  children: React.ReactNode;
  style?: any;
  accessibilityLabel?: string;
}

export interface AnimatedSharedImageProps {
  transitionData: TransitionData;
  isActive: boolean;
  onAnimationComplete: () => void;
  imageUrl: string;
}