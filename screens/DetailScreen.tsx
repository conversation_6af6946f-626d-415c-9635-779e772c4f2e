import { Image } from 'expo-image';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Platform,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { AnimatedSharedImage } from '@/components/SharedElement/AnimatedSharedImage';
import { SharedElement } from '@/components/SharedElement/SharedElement';
import { useSharedElementContext } from '@/components/SharedElement/SharedElementProvider';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { RootStackScreenProps } from '@/types/navigation';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

/**
 * DetailScreen - Displays a full-screen image with shared element transition
 */
export default function DetailScreen({ route, navigation }: RootStackScreenProps<'Detail'>) {
  const { image } = route.params;
  const context = useSharedElementContext();
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const [isAnimating, setIsAnimating] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (context.activeTransition) {
      setIsAnimating(true);
    }
  }, [context.activeTransition]);

  const handleGoBack = useCallback(() => {
    if (isAnimating) return; // Prevent multiple transitions
    
    context.startTransition(`detail-${image.id}`, `gallery-${image.id}`, () => {
      navigation.goBack();
    });
  }, [context, image.id, navigation, isAnimating]);

  const onAnimationComplete = useCallback(() => {
    setIsAnimating(false);
    if (!context.activeTransition) {
      context.endTransition();
    }
  }, [context]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    console.warn(`Detail image load error for ${image.id}`);
    setImageLoaded(false);
    setImageError(true);
    Alert.alert('Error', 'Failed to load image. Please try again.');
  }, [image.id]);

  const styles = createStyles(colorScheme ?? 'light', insets);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Animated transition overlay */}
      {context.transitionData && (
        <AnimatedSharedImage
          transitionData={context.transitionData}
          isActive={!!context.activeTransition}
          onAnimationComplete={onAnimationComplete}
          imageUrl={image.url}
        />
      )}
      
      <View style={styles.detailContainer}>
        <SharedElement 
          id={`detail-${image.id}`}
          accessibilityLabel={`Full screen view of ${image.title || 'image'}`}
        >
          <View style={styles.detailImageContainer}>
            {!imageError ? (
              <Image 
                source={{ uri: image.url }} 
                style={[
                  styles.detailImage, 
                  !imageLoaded && styles.hiddenImage
                ]}
                onLoad={handleImageLoad}
                onError={handleImageError}
                contentFit="cover"
                transition={300}
              />
            ) : (
              <View style={styles.errorContainer}>
                <ThemedText style={styles.errorText}>
                  Failed to load image
                </ThemedText>
                <TouchableOpacity 
                  style={styles.retryButton}
                  onPress={() => {
                    setImageError(false);
                    setImageLoaded(false);
                  }}
                >
                  <ThemedText style={styles.retryText}>Retry</ThemedText>
                </TouchableOpacity>
              </View>
            )}
            
            {!imageLoaded && !imageError && (
              <View style={styles.detailLoading}>
                <ActivityIndicator 
                  size="large" 
                  color={Colors[colorScheme ?? 'light'].tint} 
                />
                <ThemedText style={styles.loadingText}>Loading...</ThemedText>
              </View>
            )}
          </View>
        </SharedElement>
        
        {/* Image info overlay */}
        {imageLoaded && !imageError && (
          <View style={styles.infoContainer}>
            <ThemedText type="subtitle" style={styles.imageTitle}>
              {image.title || 'Untitled'}
            </ThemedText>
            {image.description && (
              <ThemedText style={styles.imageDescription}>
                {image.description}
              </ThemedText>
            )}
          </View>
        )}
        
        {/* Back button */}
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={handleGoBack}
          activeOpacity={0.8}
          accessibilityRole="button"
          accessibilityLabel="Go back to gallery"
          accessibilityHint="Returns to the image gallery"
        >
          <ThemedText style={styles.backButtonText}>
            ← Back to Gallery
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const createStyles = (colorScheme: 'light' | 'dark' | null, insets: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  detailContainer: {
    flex: 1,
  },
  detailImageContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailImage: {
    width: '100%',
    height: '100%',
  },
  hiddenImage: {
    opacity: 0,
  },
  detailLoading: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  errorText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: Colors[colorScheme ?? 'light'].tint,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontWeight: '600',
  },
  infoContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  imageTitle: {
    color: '#fff',
    marginBottom: 5,
  },
  imageDescription: {
    color: '#ccc',
    fontSize: 14,
    lineHeight: 20,
  },
  backButton: {
    alignSelf: 'center',
    marginTop: 20,
    marginBottom: insets.bottom + 20,
    paddingHorizontal: 30,
    paddingVertical: 15,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 25,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    ...Platform.select({
      android: {
        elevation: 3,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
    }),
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});