import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { RootStackParamList } from '@/types/navigation';

type NotFoundScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

/**
 * NotFoundScreen - Error screen displayed when navigation fails or invalid routes are accessed
 */
export default function NotFoundScreen() {
  const navigation = useNavigation<NotFoundScreenNavigationProp>();

  const handleGoHome = () => {
    navigation.navigate('Main', { screen: 'Home' });
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="title">This screen does not exist.</ThemedText>
      <TouchableOpacity onPress={handleGoHome} style={styles.link}>
        <ThemedText type="link">Go to home screen!</ThemedText>
      </TouchableOpacity>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
});