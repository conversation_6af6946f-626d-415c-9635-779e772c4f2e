import { NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';

export interface ImageData {
  id: string;
  url: string;
  title?: string;
  description?: string;
}

export type RootStackParamList = {
  Main: NavigatorScreenParams<TabParamList>;
  Detail: { image: ImageData };
  NotFound: undefined;
};

export type TabParamList = {
  Home: undefined;
  Explore: undefined;
  Gallery: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<
  RootStackParamList,
  T
>;

export type TabScreenProps<T extends keyof TabParamList> = BottomTabScreenProps<
  TabParamList,
  T
>;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}